<template name="admin_import_flow_modal">
	<div class="modal fade creator-modal flow-modal new-flow-modal" id="newFlowModal">
		<div class="modal-dialog slds-modal slds-fade-in-open modal-lg">
			<div class="slds-modal__container">
				<header class="slds-modal__header">
					<h2 class="title slds-text-heading--medium">
						<h2 class="slds-text-heading--medium">{{_ "workflow_import_flow"}}</h2>
					</h2>
				</header>

				<div class="slds-modal__content slds-p-around_medium">
					<div class="form-group slds-form-element" style="padding: 0 .75rem;">
						<label class="control-label">流程文件</label>
						<div class="import-files">
							<div>
								<div class="btn btn-default js-af-select-file" style="width: 100%">
									<input type="file" name="importFlowFile" id="importFlowFile" multiple="{{#unless isUpgrade}}multiple{{/unless}}" accept=".json,.JSON">
								</div>
								<input type="hidden" class="js-value" data-schema-key="sign">
							</div>
							<span class="help-block"></span>
						</div>
					</div>
					{{#unless isUpgrade}}
					{{#autoForm schema=schema id="importFlowForm"}}
						{{#each fields}}
							<div class="slds-grid view-page-section-row">
								<div class="slds-has-flexi-truncate slds-p-horizontal_x-small full view-page-block-item">
									<div class="slds-form-element slds-form-element_edit slds-grow slds-hint-parent slds-p-vertical_xx-small override--slds-form-element uiInput">
										{{> afQuickField name=this}}
									</div>
								</div>
							</div>
						{{/each}}
					{{/autoForm}}
					{{/unless}}
				</div>

				<footer class="slds-modal__footer">
					<button type="submit" class="slds-button slds-button_brand btn-confirm">确定</button>
					<button class="slds-button slds-button_neutral btn-cancel" data-dismiss="modal">取消</button>
				</footer>
			</div>
		</div>
	</div>
</template>