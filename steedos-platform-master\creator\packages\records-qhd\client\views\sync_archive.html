<template name="recordsQHDSyncArchive">
	<div class="qhd-sync-archive">
		<div class="box" style="border-top: 0px">
			<div class="progress progress-sm contracts-active">
				<div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
					<span class="sr-only">同步控件</span>
				</div>
			</div>
			<div class="box-header with-border">
				<div class="box-title">
					{{_ "申请单重归档"}}
				</div>
			</div>
			<div class="box-body">
				<div class="form-group">
					<div class="row">
						<div class="col-xs-10">
							<div class="input-group">
								<div class="input-group-addon">{{_ '申请单Id'}}</div>
								<input type="input" class="form-control pull-left" id="ins_ids" placeholder="请输入申请单Id, 多个Id之间请用英文逗号分隔"/>
							</div>
						</div>
						<div class="col-xs-2">
							<button type="button" class="btn btn-success instance-to-archive">同步</button>
						</div>
					</div>
				</div>

				<div class="table-responsive contracts-active-detail">

					<div class="alert alert-success alert-dismissible">
						<h4><i class="icon fa fa-check"></i> 同步已完成</h4>
						共{{syncData.length}}条数据
					</div>

					<table class="table no-margin">
						<thead>
						<tr>
							<th>文件标题</th>
							<th>提交人</th>
							<th>提交时间</th>
							<th>状态</th>
						</tr>
						</thead>
						<tbody>
							{{#each syncData}}
								<tr>
									<td><a href="/workflow/space/{{space}}/view/readonly/{{_id}}" target="_blank">{{name}}</a></td>
									<td>{{applicant_name}}</td>
									<td>{{format submit_date}}</td>
									<td>
										{{#if is_archived}}
											<span class="label label-success">成功</span>
										{{else}}
											<span class="label label-error">失败</span>
										{{/if}}
									</td>
								</tr>
							{{/each}}
						</tbody>

					</table>
				</div>
			</div>
		</div>
	</div>
</template>

