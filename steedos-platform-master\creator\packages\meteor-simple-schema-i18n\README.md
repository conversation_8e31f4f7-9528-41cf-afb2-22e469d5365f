```diff
- NOTE: This package is not maintained anymore.
- If you want to help, please reach <NAME_EMAIL>
```

Meteor Simple-Schema i18n
=========================

Internationalization for [aldeed:simple-schema](http://github.com/aldeed/meteor-simple-schema) error messages, powered by [tap:i18n](https://github.com/TAPevents/tap-i18n).  

UPDATE: To get reactively translated labels, placeholders and options in your autoforms, additionally use [gwendall:autoform-i18n](http://github.com/gwendall/meteor-autoform-i18n).

Installation  
------------

``` sh
meteor add gwendall:simple-schema-i18n
```

Contribute
----------

Help us building a complete i18n solution by translating simple-schema. To do so, please fork the repo and submit a pull request after adding / modifying any language file (in the /i18n folder).  
The language code you should use to name new i18n.json files can be found in [this list](https://github.com/TAPevents/tap-i18n/blob/master/lib/plugin/etc/language_names.js).

Credits
-------

[@microstudi](http://github.com/microstudi),
[@StorytellerCZ](http://github.com/StorytellerCZ),
[@jlourenco](http://github.com/jlourenco),
[@cypa1](http://github.com/cypa1),
[@maredudd](http://github.com/maredudd),
[@kjetilge](http://github.com/kjetilge),
[@Xiangshen-Meng](http://github.com/Xiangshen-Meng),
[@panayi](http://github.com/panayi),
[@daveeel](http://github.com/daveeel),
[@nirleka](http://github.com/nirleka),
[@uzumaxy](http://github.com/uzumaxy),
[@coderany](http://github.com/coderany),
[@timbrandin](http://github.com/timbrandin),
[@Adiel-Sharabi](http://github.com/Adiel-Sharabi),
[@splendido](http://github.com/splendido),
[@Findegil](http://github.com/Findegil),
[@ThePanuto](http://github.com/ThePanuto),
[@achtan](http://github.com/achtan),
[@laosb](http://github.com/laosb),
[@serkandurusoy](http://github.com/serkandurusoy),
[@rzmz](http://github.com/rzmz),
[@mahmoudkm](http://github.com/mahmoudkm),
[@xaiki](http://github.com/xaiki),
[@Tim-W](http://github.com/Tim-W),
[@DavidSichau](http://github.com/DavidSichau),
[@Szayet](http://github.com/Szayet)

To do
-----

- [ ] Afrikaans
- [ ] Akan
- [ ] Albanian
- [ ] Amharic
- [x] Arabic
- [ ] Armenian
- [ ] Aromanian
- [ ] Assamese
- [ ] Azerbaijani
- [ ] Azerbaijani (Turkey)
- [ ] Bashkir
- [ ] Basque
- [ ] Belarusian
- [ ] Bengali
- [ ] Bosnian
- [x] Bulgarian
- [ ] Burmese
- [x] Catalan
- [ ] Catalan (Balear)
- [ ] Chinese
- [x] Chinese (China)
- [x] Chinese (Hong Kong)
- [x] Chinese (Taiwan)
- [ ] Corsican
- [ ] Croatian
- [x] Czech
- [ ] Danish
- [x] Dutch
- [ ] Dutch (Belgium)
- [x] English
- [x] English (Australia)
- [x] English (Canada)
- [x] English (UK)
- [ ] Esperanto
- [x] Estonian
- [ ] Faroese
- [ ] Finnish
- [x] French (Belgium)
- [x] French (France)
- [ ] Frisian
- [ ] Fulah
- [ ] Galician
- [ ] Georgian
- [x] German
- [x] Greek
- [ ] Guaraní
- [ ] Hawaiian
- [ ] Hazaragi
- [x] Hebrew
- [ ] Hindi
- [x] Hungarian
- [ ] Icelandic
- [x] Indonesian
- [ ] Irish
- [x] Italian
- [x] Japanese
- [ ] Javanese
- [ ] Kannada
- [ ] Kazakh
- [ ] Khmer
- [ ] Kinyarwanda
- [ ] Kirghiz
- [ ] Korean
- [ ] Kurdish (Sorani)
- [ ] Lao
- [ ] Latvian
- [ ] Limburgish
- [ ] Lithuanian
- [ ] Luxembourgish
- [ ] Macedonian
- [ ] Malagasy
- [ ] Malay
- [ ] Malayalam
- [ ] Marathi
- [ ] Mingrelian
- [ ] Mongolian
- [ ] Montenegrin
- [ ] Nepali
- [x] Norwegian (Bokmål)
- [ ] Norwegian (Nynorsk)
- [ ] Ossetic
- [ ] Pashto
- [ ] Persian
- [ ] Persian (Afghanistan)
- [x] Polish
- [x] Portuguese (Brazil)
- [x] Portuguese (Portugal)
- [ ] Punjabi
- [ ] Rohingya
- [ ] Romanian
- [x] Russian
- [ ] Rusyn
- [ ] Sakha
- [ ] Sanskrit
- [ ] Sardinian
- [ ] Scottish Gaelic
- [ ] Serbian
- [ ] Sindhi
- [ ] Sinhala
- [x] Slovak
- [ ] Slovenian
- [ ] Somali
- [ ] South Azerbaijani
- [ ] Spanish (Argentina)
- [ ] Spanish (Chile)
- [ ] Spanish (Colombia)
- [ ] Spanish (Mexico)
- [ ] Spanish (Peru)
- [ ] Spanish (Puerto Rico)
- [x] Spanish (Spain)
- [ ] Spanish (Venezuela)
- [ ] Sundanese
- [ ] Swahili
- [x] Swedish
- [ ] Swiss German
- [ ] Tagalog
- [ ] Tajik
- [ ] Tamil
- [ ] Tamil (Sri Lanka)
- [ ] Tatar
- [ ] Telugu
- [ ] Thai
- [ ] Tibetan
- [ ] Tigrinya
- [x] Turkish
- [ ] Turkmen
- [x] Ukrainian
- [ ] Uighur
- [ ] Ukrainian
- [ ] Urdu
- [ ] Uzbek
- [ ] Vietnamese
- [ ] Walloon
- [x] Welsh
