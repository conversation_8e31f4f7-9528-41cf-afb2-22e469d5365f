# socket-stream-client
[Source code of released version](https://github.com/meteor/meteor/tree/master/packages/socket-stream-client) | [Source code of development version](https://github.com/meteor/meteor/tree/devel/packages/socket-stream-client)
***

This package provides the `ClientStream` abstraction used by the
[`ddp-client`](https://github.com/meteor/meteor/tree/devel/packages/ddp-client)
package.

