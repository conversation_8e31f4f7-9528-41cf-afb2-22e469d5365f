# Selectize 

v0.12.1

[Selectize](http://brianreavis.github.io/selectize.js/) is the hybrid of a textbox and `<select>` element. It's a jQuery-based plugin that can be used for tagging, contact lists, country selectors, and lots more.

## Install

`meteor add jeremy:selectize`

This Meteor package currently only loads the plugin (with stylesheet).  Just add to your app and set it up manually as you would in any other application.


[Homepage](http://brianreavis.github.io/selectize.js/)

[Github](https://github.com/brianreavis/selectize.js)

[Documentation](https://github.com/brianreavis/selectize.js/tree/master/docs)  

[Examples](https://github.com/brianreavis/selectize.js/tree/master/examples)



