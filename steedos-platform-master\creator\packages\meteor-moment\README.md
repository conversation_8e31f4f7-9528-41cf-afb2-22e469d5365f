Packaging [Moment](momentjs.org) for [Meteor.js](http://meteor.com).


# Meteor

If you're new to Meteor, here's what the excitement is all about -
[watch the first two minutes](https://www.youtube.com/watch?v=fsi0aJ9yr2o); you'll be hooked by 1:28.

That screencast is from 2012. In the meantime, Meteor has become a mature JavaScript-everywhere web
development framework. Read more at [Why Meteor](http://www.meteorpedia.com/read/Why_Meteor).


# Issues

If you encounter an issue while using this package, please CC @dandv when you file it in this repo.


# DONE

* Simple test. Should be enough.


# TODO

* Add other tests; however, that is overkill, and the responsibiity of Moment, not of the Meteor integration.
